# Multi-Model Question Answering System

This is a Python-based HTTP service that sends questions to multiple Chinese AI models simultaneously using multithreading and then summarizes the responses.

## Features

- Concurrently query multiple Chinese AI models
- Automatic question classification using a small model to determine which models to use
- Configurable strategies for different question types
- Automatic summarization of responses
- Fast execution using multithreading
- No VPN required for Chinese models

## Prerequisites

- Python 3.7+
- Flask
- Requests

## Installation

1. Clone the repository
2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Configuration

Set up your API keys as environment variables:

```bash
export QWEN_API_KEY=your_qwen_api_key
export ZHIPU_API_KEY=your_zhipu_api_key
export BAIDU_API_KEY=your_baidu_api_key
export DEEPSEEK_API_KEY=your_deepseek_api_key
export KIMI_API_KEY=your_kimi_api_key
```

Or modify the [config.py](file:///opt/mchat/config.py) file directly.

## Usage

Start the server:

```bash
python app.py
```

The server will start on `http://localhost:5000`.

### Ask a question (with automatic classification)

```bash
curl -X POST http://localhost:5000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "用Python写一个快速排序算法"}'
```

### Ask a question with specific type (manual override)

```bash
curl -X POST http://localhost:5000/ask/technical \
  -H "Content-Type: application/json" \
  -d '{"question": "如何优化数据库查询性能？"}'
```

### List available models

```bash
curl http://localhost:5000/models
```

### List available strategies

```bash
curl http://localhost:5000/strategies
```

## How It Works

1. When a question is received, the system first uses a small model (Qwen-turbo) to classify the question type
2. Based on the classification result, the system selects an appropriate combination of models
3. The question is sent to multiple models simultaneously using multithreading
4. Responses from all models are collected and summarized by another model

## Supported Models

1. **通义千问 (Qwen)** - From Alibaba Cloud
2. **智谱清言 (Zhipu AI)** - From Zhipu AI
3. **文心一言 (Ernie Bot)** - From Baidu
4. **DeepSeek** - From DeepSeek
5. **Kimi** - From Moonshot AI

## Strategies

- **default**: Qwen + Zhipu (for general questions)
- **technical**: Qwen + Zhipu + Baidu (for technical questions)
- **creative**: Qwen + Zhipu (for creative questions)
- **analytical**: Qwen + Baidu (for analytical questions)
- **coding**: Qwen + DeepSeek + Kimi (for code-related questions)
- **long_text**: Qwen + Kimi (for long text processing)

The system automatically selects the appropriate strategy based on the question content. You can also manually specify a strategy by using the corresponding endpoint.

## Model Configuration and Policy (MCP)

### Adding a New Model

To add a new model to the system, follow these steps:

1. Add the model configuration in [config.py](file:///opt/mchat/config.py):
   ```python
   MODEL_CONFIGS = {
       # ... existing models ...
       "new_model": {
           "name": "New Model Name",
           "api_url": "https://api.newmodel.com/v1/chat/completions",
           "api_key": os.environ.get("NEW_MODEL_API_KEY", "your_new_model_api_key"),
           "model": "new-model-version",
       }
   }
   ```

2. Update the `get_headers` function in [app.py](file:///opt/mchat/app.py) to handle authentication for the new model:
   ```python
   if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier", "new_model"]:
       headers["Authorization"] = f"Bearer {config['api_key']}"
   ```

3. Update the response parsing logic in the `call_model` function in [app.py](file:///opt/mchat/app.py) if needed:
   ```python
   if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier", "new_model"]:
       content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
   ```

### Adding a New Strategy

To add a new strategy for a specific question type:

1. Add the strategy in [config.py](file:///opt/mchat/config.py):
   ```python
   STRATEGY_CONFIG = {
       # ... existing strategies ...
       "new_strategy": ["qwen", "new_model"]
   }
   ```

2. Optionally, update the classifier prompt in [config.py](file:///opt/mchat/config.py) to include the new question type:
   ```python
   CLASSIFIER_PROMPT = """...
   n. new_strategy - Description of when to use this strategy
   ..."""
   ```

## Using with CherryStudio

To use this MCP service with CherryStudio:

1. Start both the MCP service and the CherryStudio adapter:
   ```bash
   # Terminal 1: Start the main MCP service
   python app.py
   
   # Terminal 2: Start the CherryStudio adapter
   python cherry_studio_adapter.py
   ```

2. In CherryStudio, add a custom OpenAI-compatible model with the following settings:
   - API Key: `any-value` (can be any value, as our adapter doesn't validate it)
   - API Endpoint: `http://localhost:8000/v1`
   - Model Name: `mcp-multi-model`

3. You can now use CherryStudio to send messages to the MCP service, which will:
   - Receive your message from CherryStudio
   - Send it to multiple AI models concurrently
   - Return a summarized response from all models

The adapter translates between CherryStudio's OpenAI-compatible API format and our MCP service's format, allowing seamless integration.

## Customization

You can modify the [config.py](file:///opt/mchat/config.py) file to:
1. Add or remove models
2. Change strategies for different question types
3. Update model parameters
4. Modify the question classifier prompt

## API Response Format

```json
{
  "question": "The question asked",
  "question_type": "auto or manually specified type",
  "models_used": ["List of models used"],
  "responses": [
    {
      "model": "Model name",
      "response": "Model's response",
      "status": "success or error"
    }
  ],
  "summary": "Summary of all responses"
}
```